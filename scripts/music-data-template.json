{"coverUrls": ["https://example.com/cover1.jpg", "https://example.com/cover2.jpg", "https://example.com/cover3.jpg", "https://example.com/cover4.jpg", "https://example.com/cover5.jpg", "https://example.com/cover6.jpg", "https://example.com/cover7.jpg", "https://example.com/cover8.jpg", "https://example.com/cover9.jpg", "https://example.com/cover10.jpg"], "musicData": [{"title": "夜空中最亮的星", "labelName": "星光唱片", "albumName": "星空物语", "trackInfo": "一首关于梦想和希望的歌曲", "primaryLanguage": "zh", "upc": "123456789001", "isrc": "CNRC24000001", "primaryGenreId": "pop", "secondaryGenreId": "folk", "copyrightName": "星光音乐版权公司", "copyrightYear": 2024, "phonogramCopyright": "星光录音版权", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital"], "mediaUrls": ["https://example.com/audio/track001.mp3"]}, {"title": "青春的回忆", "labelName": "青春唱片", "albumName": "时光倒流", "trackInfo": "回忆青春岁月的温暖歌曲", "primaryLanguage": "zh", "upc": "123456789002", "isrc": "CNRC24000002", "primaryGenreId": "pop", "secondaryGenreId": "rock", "copyrightName": "青春音乐版权公司", "copyrightYear": 2024, "phonogramCopyright": "青春录音版权", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3"], "releaseOptions": ["digital"], "mediaUrls": ["https://example.com/audio/track002.mp3"]}, {"title": "城市的灯火", "labelName": "都市音乐", "albumName": "霓虹夜色", "trackInfo": "描绘城市夜景的现代歌曲", "primaryLanguage": "zh", "upc": "123456789003", "isrc": "CNRC24000003", "primaryGenreId": "electronic", "secondaryGenreId": "pop", "copyrightName": "都市音乐版权", "copyrightYear": 2024, "phonogramCopyright": "都市录音版权", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "flac"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track003.mp3"]}, {"title": "海边的风", "labelName": "海洋唱片", "albumName": "蓝色心情", "trackInfo": "清新的海边风情歌曲", "primaryLanguage": "zh", "upc": "123456789004", "isrc": "CNRC24000004", "primaryGenreId": "folk", "secondaryGenreId": "acoustic", "copyrightName": "海洋音乐版权", "copyrightYear": 2024, "phonogramCopyright": "海洋录音版权", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital"], "mediaUrls": ["https://example.com/audio/track004.mp3"]}, {"title": "雨后彩虹", "labelName": "彩虹音乐", "albumName": "希望之光", "trackInfo": "充满希望和正能量的歌曲", "primaryLanguage": "zh", "upc": "123456789005", "isrc": "CNRC24000005", "primaryGenreId": "pop", "secondaryGenreId": "inspirational", "copyrightName": "彩虹音乐版权", "copyrightYear": 2024, "phonogramCopyright": "彩虹录音版权", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track005.mp3"]}]}