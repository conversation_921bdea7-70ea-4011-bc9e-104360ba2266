# 快速开始指南

## 🚀 快速上传50条音乐信息

### 第一步：准备数据
已经为你生成了50条示例音乐数据，文件位于：
```
scripts/music-data-50-tracks.json
```

### 第二步：配置封面URL
编辑 `scripts/music-data-50-tracks.json` 文件，将 `coverUrls` 数组中的示例URL替换为你的实际封面URL：

```json
{
  "coverUrls": [
    "你的封面URL1",
    "你的封面URL2",
    "你的封面URL3",
    "..."
  ]
}
```

### 第三步：获取认证Token

1. 在浏览器中登录你的账户
2. 打开开发者工具（F12）
3. 切换到 Network 标签
4. 刷新页面或进行任何API请求
5. 找到任意一个API请求，查看请求头中的 `Authorization` 字段
6. 复制 `Bearer ` 后面的token值

### 第四步：配置脚本

编辑 `scripts/batch-upload-music.js` 文件，修改配置：

```javascript
const CONFIG = {
  // 根据你的环境选择正确的API地址
  BASE_URL: 'http://localhost:3000/api', // 开发环境
  // BASE_URL: 'https://your-production-api.com/api', // 生产环境
  
  // 将这里替换为你的实际token
  AUTH_TOKEN: 'your-actual-token-here',
  
  // 请求间隔（毫秒）
  REQUEST_INTERVAL: 1000,
  
  // 数据文件路径（使用生成的50条数据）
  DATA_FILE: path.join(__dirname, 'music-data-50-tracks.json'),
};
```

### 第五步：运行上传脚本

```bash
node scripts/batch-upload-music.js
```

## 📋 脚本会做什么

1. **加载数据**: 从JSON文件读取50条音乐数据
2. **验证配置**: 检查token和数据文件是否正确
3. **获取元数据**: 显示可用的音乐类型、音频格式等
4. **批量上传**: 逐个提交音乐作品，每次请求间隔1秒
5. **显示结果**: 实时显示上传进度和结果
6. **保存报告**: 将详细结果保存到JSON文件

## 📊 预期输出

```
🎵 音乐作品批量上传工具
================================
✅ 成功加载 50 条音乐数据
✅ 成功加载 10 个封面URL

📋 获取元数据信息...
🎵 可用的音乐类型:
  - pop: 流行音乐
  - rock: 摇滚音乐
  - folk: 民谣
  ...

⚠️  请确认以下信息:
📊 准备上传 50 首歌曲
🖼️  可用封面 10 个
🌐 API地址: http://localhost:3000/api
⏱️  请求间隔: 1000ms

🚀 开始批量上传音乐作品...

📝 [1/50] 处理中...
正在提交: 夜空中最亮的星 1
✅ 成功提交: 夜空中最亮的星 1, ID: track_123456
⏳ 等待 1000ms...

📝 [2/50] 处理中...
正在提交: 青春的回忆 2
✅ 成功提交: 青春的回忆 2, ID: track_123457
⏳ 等待 1000ms...

...

📊 上传完成统计:
✅ 成功: 48 首
❌ 失败: 2 首

✅ 成功列表:
  - 夜空中最亮的星 1 (ID: track_123456)
  - 青春的回忆 2 (ID: track_123457)
  ...

❌ 失败列表:
  - 某首歌曲: 错误原因

🎉 批量上传完成!
📄 结果已保存到: upload-results-1703123456789.json
```

## ⚠️ 注意事项

1. **Token有效期**: 如果上传过程中出现401错误，说明token已过期，需要重新获取
2. **网络稳定**: 确保网络连接稳定，上传50首歌曲大约需要50-60秒
3. **数据检查**: 上传前请检查音乐数据的完整性
4. **封面URL**: 确保封面图片URL可以正常访问
5. **备份数据**: 建议在上传前备份你的音乐数据

## 🔧 自定义数据

如果你想使用自己的音乐数据而不是生成的示例数据：

1. 复制 `music-data-template.json` 为新文件
2. 编辑新文件，添加你的音乐信息
3. 修改 `batch-upload-music.js` 中的 `DATA_FILE` 配置指向你的文件

## 🆘 故障排除

- **Token错误**: 重新登录获取新token
- **网络错误**: 检查API地址是否正确
- **数据格式错误**: 检查JSON文件格式
- **权限不足**: 确保账户有上传音乐的权限

## 📞 需要帮助？

如果遇到问题，请检查：
1. 控制台输出的错误信息
2. 生成的结果文件中的详细错误
3. 网络连接和API服务状态
