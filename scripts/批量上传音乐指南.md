# 批量上传50条音乐信息指南

## 📁 文件说明

我已经为你创建了完整的批量上传工具，包含以下文件：

### 核心脚本
- `batch-upload-music.js` - 主要的批量上传脚本
- `generate-music-data.js` - 生成50条示例音乐数据的脚本
- `test-api-connection.js` - 测试API连接的脚本

### 数据文件
- `music-data-50-tracks.json` - 已生成的50条音乐数据（可直接使用）
- `music-data-template.json` - 5条示例数据模板

### 文档
- `README.md` - 详细使用说明
- `QUICK_START.md` - 快速开始指南
- `批量上传音乐指南.md` - 本文件

## 🚀 快速使用步骤

### 1. 准备封面URL
编辑 `music-data-50-tracks.json` 文件，将封面URL替换为你的实际URL：

```json
{
  "coverUrls": [
    "你的封面URL1",
    "你的封面URL2",
    "你的封面URL3",
    "..."
  ]
}
```

### 2. 获取认证Token
1. 登录你的账户
2. 打开浏览器开发者工具（F12）
3. 查看任意API请求的Authorization头
4. 复制Bearer后面的token值

### 3. 配置脚本
编辑 `batch-upload-music.js`，修改配置：

```javascript
const CONFIG = {
  BASE_URL: 'http://localhost:3000/api', // 你的API地址
  AUTH_TOKEN: 'your-actual-token-here', // 你的实际token
  REQUEST_INTERVAL: 1000,
  DATA_FILE: path.join(__dirname, 'music-data-50-tracks.json'),
};
```

### 4. 测试连接（可选）
```bash
node scripts/test-api-connection.js
```

### 5. 开始批量上传
```bash
node scripts/batch-upload-music.js
```

## 📊 数据结构说明

每条音乐数据包含以下字段：

```json
{
  "title": "歌曲标题",
  "labelName": "唱片公司名称", 
  "albumName": "专辑名称",
  "trackInfo": "歌曲描述",
  "primaryLanguage": "zh",
  "upc": "UPC码",
  "isrc": "ISRC码", 
  "primaryGenreId": "主要音乐类型",
  "secondaryGenreId": "次要音乐类型",
  "copyrightName": "版权所有者",
  "copyrightYear": 2024,
  "phonogramCopyright": "录音版权所有者",
  "phonogramCopyrightYear": 2024,
  "audioFormats": ["mp3", "wav"],
  "releaseOptions": ["digital", "streaming"],
  "mediaUrls": ["音频文件URL"]
}
```

## 🎵 已生成的50条数据

脚本已经为你生成了50条示例音乐数据，包括：

- **歌曲类型**: 流行、摇滚、民谣、电子、爵士等
- **专辑分组**: 每10首歌曲为一个专辑卷
- **唯一标识**: 每首歌都有唯一的UPC和ISRC码
- **完整信息**: 包含所有必需的版权和技术信息

示例歌曲：
1. 夜空中最亮的星 1 - 星空物语 Vol.1
2. 青春的回忆 2 - 时光倒流 Vol.1  
3. 城市的灯火 3 - 霓虹夜色 Vol.1
4. 海边的风 4 - 蓝色心情 Vol.1
5. 雨后彩虹 5 - 希望之光 Vol.1
... (共50首)

## ⚙️ 脚本功能

### 批量上传脚本功能：
- ✅ 自动加载JSON数据文件
- ✅ 验证配置和数据完整性
- ✅ 获取并显示可用的音乐类型
- ✅ 批量提交音乐作品（每次间隔1秒）
- ✅ 实时显示上传进度
- ✅ 详细的成功/失败统计
- ✅ 自动保存结果到JSON文件

### 测试脚本功能：
- ✅ 测试API连接状态
- ✅ 验证认证token有效性
- ✅ 检查各个API端点
- ✅ 显示可用的元数据信息

## 📈 预期结果

成功运行后，你将看到：

```
🎵 音乐作品批量上传工具
================================
✅ 成功加载 50 条音乐数据
✅ 成功加载 10 个封面URL

🚀 开始批量上传音乐作品...
📊 总计: 50 首歌曲

📝 [1/50] 处理中...
✅ 成功提交: 夜空中最亮的星 1, ID: track_123456

📝 [2/50] 处理中...  
✅ 成功提交: 青春的回忆 2, ID: track_123457

... (继续处理剩余48首)

📊 上传完成统计:
✅ 成功: 50 首
❌ 失败: 0 首

🎉 批量上传完成!
📄 结果已保存到: upload-results-1703123456789.json
```

## ⚠️ 重要提醒

1. **封面URL**: 请确保替换为你的实际封面图片URL
2. **音频URL**: 如果需要，也要替换音频文件URL
3. **Token有效期**: Token可能会过期，如遇401错误请重新获取
4. **网络稳定**: 上传50首歌曲大约需要50-60秒
5. **数据备份**: 建议保存一份原始数据作为备份

## 🔧 自定义修改

如果你想修改音乐信息：

1. **修改现有数据**: 直接编辑 `music-data-50-tracks.json`
2. **重新生成数据**: 修改 `generate-music-data.js` 中的模板，然后重新运行
3. **使用自己的数据**: 按照JSON格式创建你自己的数据文件

## 🆘 故障排除

常见问题及解决方案：

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 401错误 | Token无效或过期 | 重新获取token |
| 网络超时 | 网络连接问题 | 检查网络和API地址 |
| 数据格式错误 | JSON格式不正确 | 检查JSON语法 |
| 权限不足 | 账户权限问题 | 确认账户有上传权限 |

## 📞 技术支持

如果遇到问题：
1. 查看控制台输出的详细错误信息
2. 检查生成的结果文件
3. 运行测试脚本验证API连接
4. 确认所有配置项都正确设置

祝你批量上传成功！🎉
