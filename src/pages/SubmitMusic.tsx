import React, { useState, useEffect, useMemo } from 'react';
import LanguageList from 'language-list';
import AlbumCoverUpload from '@/components/AlbumCoverUpload';
import { api } from '@/services';
import type {
  AudioFormat,
  ReleaseOption,
  Genre,
  SubmitTrackRequest,
} from '@/types/api';
import MusicUpload from '@/components/MusicUpload';
const languageListInstance = new LanguageList();

// console.log(languageListInstance.getLanguageCodes());
// console.log(languageListInstance.getData());
import {
  LANGUAGE_OPTIONS,
  type LanguageOption,
} from '@/utils/language-options';
import {
  Form,
  message,
  Input,
  Button,
  DatePicker,
  Select,
  Checkbox,
  ConfigProvider,
} from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import type * as dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;

/**
 * 扩展提交音乐作品表单类型，支持 dayjs 类型的字段
 */
interface SubmitTrackRequestForm
  extends Omit<
    SubmitTrackRequest,
    | 'copyrightYear'
    | 'originalReleaseDate'
    | 'streetDate'
    | 'phonogramCopyrightYear'
  > {
  copyrightYear: dayjs.Dayjs;
  originalReleaseDate: dayjs.Dayjs;
  streetDate: dayjs.Dayjs;
  phonogramCopyrightYear: dayjs.Dayjs;
}

const SubmitMusic: React.FC = () => {
  const { t, language } = useLanguage();
  const [form] = Form.useForm<SubmitTrackRequestForm>();
  const [loading, setLoading] = useState(false);
  const [coverArtUrl, setCoverArtUrl] = useState<string>('');
  const [audioFormats, setAudioFormats] = useState<AudioFormat[]>([]);
  const [releaseOptions, setReleaseOptions] = useState<ReleaseOption[]>([]);
  const [genres, setGenres] = useState<Genre[]>([]);
  const [mediaUrls, setMediaUrls] = useState<string>('');

  const languageOptions = useMemo(() => {
    return LANGUAGE_OPTIONS.map(item => ({
      value: item.tag,
      label: language === 'zh' ? item.name_zh : item.name_en,
    }));
  }, [language]);

  const loadOptions = async () => {
    const [audioFormatsRes, releaseOptionsRes, genresRes] = await Promise.all([
      api.music.getDefaultAudioFormats(),
      api.music.getDefaultReleaseOptions(),
      api.music.getDefaultGenres(),
    ]);
    if (audioFormatsRes.code === 200) {
      setAudioFormats(audioFormatsRes.body);
    }
    if (releaseOptionsRes.code === 200) {
      setReleaseOptions(releaseOptionsRes.body);
    }
    if (genresRes.code === 200) {
      setGenres(genresRes.body);
    }
    // console.log(audioFormats, releaseOptions, genres);
  };

  useEffect(() => {
    loadOptions();
  }, []);

  const handleSubmit = async (values: SubmitTrackRequestForm) => {
    setLoading(true);
    // if (!coverArtUrl) {
    //   message.error(t('submitMusic.placeholders.coverArtRequired'));
    //   return;
    // }
    try {
      console.log('values', values.originalReleaseDate);
      // 确保包含封面 URL
      const submitData: SubmitTrackRequest = {
        ...values,
        originalReleaseDate: values.originalReleaseDate.valueOf(),
        streetDate: values.streetDate.valueOf(),
        copyrightYear: values.copyrightYear.year(),
        phonogramCopyrightYear: values.phonogramCopyrightYear.year(),
        phonogramCopyright: values.phonogramCopyright,
        coverArtUrl: coverArtUrl,
      };
      console.log('提交的数据:', submitData);
      // // TODO: 调用API提交数据
      // const res = await api.music.submitTrack(submitData);
      // if (res.code === 200) {
      // message.success(t('common.submitSuccess'));
      // form.resetFields();
      // } else {
      //   message.error(t('common.submitFailed'));
      // }
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
  };

  // 处理专辑封面上传成功
  const handleCoverUploadSuccess = async (url: string) => {
    try {
      setCoverArtUrl(url);
      // 同时更新表单字段
      form.setFieldValue('coverArtUrl', url);
      message.success(t('common.saveSuccess'));
    } catch (error) {
      console.error('专辑封面上传失败:', error);
      message.error(t('common.saveFailed'));
    }
  };

  // 自定义专辑封面上传请求
  const customCoverUploadRequest = async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      // TODO 现在使用上传头像的API处理专辑封面
      const response = await api.user.uploadAvatar(file);
      onSuccess({ url: response.url });
    } catch (error) {
      console.error('专辑封面上传失败:', error);
      onError(error);
    }
  };

  // 处理附加媒体文件上传成功
  const handleAdditionalUploadSuccess = async (url: string) => {
    setMediaUrls(url);
    form.setFieldValue('mediaUrls', url);
  };

  const rules = {
    title: [{ required: true }],
    labelName: [{ required: true }],
    primaryLanguage: [{ required: true }],
    primaryGenre: [{ required: true }],
    secondaryGenre: [{ required: true }],
    originalReleaseDate: [{ required: true }],
    streetDate: [{ required: true }],
    trackIntro: [{ required: true }],
    copyrightName: [{ required: true }],
    copyrightYear: [{ required: true }],
    phonogramCopyrightYear: [{ required: true }],
    phonogramCopyright: [{ required: true }],
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            labelFontSize: 12,
            labelColor: 'var(--color-label)',
          },
        },
      }}
    >
      <div className="min-h-screen bg-[#0d0d0d] relative min-w-1000px">
        <div className="pl-8 pr-8 pt-6 max-w-1800px mr-auto">
          <Form
            requiredMark={false}
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className="max-w-none"
          >
            {/* Artist Info Section */}
            {/* <div className="mb-10">
            <h2 className="text-white text-[22px] font-bold mb-6 ">
              Artist Info
            </h2>

            <div className="mb-6">
              <div className="text-label text-[18px] font-bold mb-5 ">
                Primary Artist (Stage Name):
              </div>
              <div className="w-[854px]">
                <Input
                  className="s-submit-input"
                  placeholder="value25"
                  style={{ borderRadius: 0 }}
                />
              </div>
            </div>

            <div className="mb-6">
              <div className="text-label text-[18px] font-bold mb-5 ">
                Artist Bio (0 to 400 characters):
              </div>
              <TextArea
                className="bg-[#282828] border-none text-[#999999] text-[14px] leading-[27px] px-4 py-3"
                style={{
                  borderRadius: 0,
                  height: '139px',
                  resize: 'none',
                  width: '1462px',
                }}
                placeholder='Koji Takanashi, born on April 13, 1963 in Tokyo, Japan, is a Japanese composer, arranger, keyboard player, and a member of the Japanese hard rock band "Musashi". He is currently mainly engaged in the production of animation music. The classic tracks in a series of well-known animations and TV works such as "Naruto", "Pretty Cure", "Fairy Tail", "Hell Girl", "Super Star God", and "Log Horizon" are all from his hand.'
                maxLength={400}
              />
            </div>
          </div> */}

            {/* Track Info Section */}
            <div className="mb-10">
              <h2 className="text-white text-[22px] font-bold mb-8 ">
                {t('submitMusic.trackInfo')}
              </h2>

              <div className="flex gap-8 mb-8">
                <div className="w-[340px] h-[340px]">
                  <AlbumCoverUpload
                    coverArtUrl={coverArtUrl}
                    size={340}
                    onUploadSuccess={handleCoverUploadSuccess}
                    customRequest={customCoverUploadRequest}
                  />
                </div>

                <div className="flex-1">
                  <Form.Item
                    className="s-submit-input-label"
                    labelAlign="left"
                    layout="horizontal"
                    colon={false}
                    label={
                      <div className="w-60px">{t('submitMusic.title')}:</div>
                    }
                    messageVariables={{
                      label: `${t('submitMusic.title')}`,
                    }}
                    rules={rules.title}
                    name="title"
                  >
                    <Input
                      className="s-submit-input"
                      placeholder={t('submitMusic.placeholders.title')}
                    />
                  </Form.Item>
                  <Form.Item
                    className="s-submit-input-label"
                    labelAlign="left"
                    layout="horizontal"
                    colon={false}
                    label={
                      <div className="w-60px">
                        {t('submitMusic.labelName')}:
                      </div>
                    }
                    messageVariables={{
                      label: `${t('submitMusic.labelName')}`,
                    }}
                    rules={rules.labelName}
                    name="labelName"
                  >
                    <Input
                      className="s-submit-input"
                      placeholder={t('submitMusic.placeholders.labelName')}
                    />
                  </Form.Item>
                  <Form.Item
                    labelAlign="left"
                    label={`${t('submitMusic.trackIntro')}:`}
                    rules={rules.trackIntro}
                    name="trackIntro"
                    messageVariables={{
                      label: `${t('submitMusic.trackIntro2')}`,
                    }}
                  >
                    <TextArea
                      className="s-submit-input "
                      placeholder={t('submitMusic.placeholders.trackIntro')}
                      style={{ borderRadius: 0 }}
                    />
                  </Form.Item>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-8 mb-6">
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.albumNameOptional')}:
                    </div>
                  }
                  name="albumName"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.title')}
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  rules={rules.primaryLanguage}
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.primaryLanguage')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.primaryLanguage')}`,
                  }}
                  name="primaryLanguage"
                >
                  <Select
                    className="s-submit-select !h-42px"
                    placeholder={t('submitMusic.selectLanguage')}
                  >
                    {languageOptions.map(item => (
                      <Option value={item.value} key={item.value}>
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.UPCOptional')}:
                    </div>
                  }
                  name="upc"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.upc')}
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.ISRCOptional')}:
                    </div>
                  }
                  name="isrc"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.isrc')}
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.primaryGenre')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.primaryGenre')}`,
                  }}
                  rules={rules.primaryGenre}
                  name="primaryGenre"
                >
                  <Select className="s-submit-select !h-42px">
                    {genres.map(genre => (
                      <Option value={genre.code} key={genre.code}>
                        {genre.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.secondaryGenre')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.secondaryGenre')}`,
                  }}
                  rules={rules.secondaryGenre}
                  name="secondaryGenre"
                >
                  <Select className="s-submit-select !h-42px">
                    {genres.map(genre => (
                      <Option value={genre.code} key={genre.code}>
                        {genre.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  rules={rules.originalReleaseDate}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      {t('submitMusic.originalReleaseDate')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.originalReleaseDate')}`,
                  }}
                  name="originalReleaseDate"
                >
                  <DatePicker
                    className="s-submit-datepicker !h-42px"
                    format="DD/MM/YYYY"
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  rules={rules.streetDate}
                  colon={false}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      {t('submitMusic.streetDate')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.streetDate')}`,
                  }}
                  name="streetDate"
                >
                  <DatePicker
                    className="s-submit-datepicker !h-42px"
                    format="DD/MM/YYYY"
                  />
                </Form.Item>
              </div>
            </div>
            {/* 版权信息Section */}
            <div className="mb-10">
              <h2 className="text-white text-[22px] font-bold mb-8 ">
                {t('submitMusic.copyrightInfo')}
              </h2>
              <div className="grid grid-cols-3 gap-8 mb-6">
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  colon={false}
                  messageVariables={{
                    label: `©${t('submitMusic.year')}`,
                  }}
                  rules={rules.copyrightYear}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      ©{t('submitMusic.year')}:
                    </div>
                  }
                  name="copyrightYear"
                >
                  <DatePicker
                    className="s-submit-datepicker !h-42px"
                    placeholder={t('submitMusic.placeholders.datePicker')}
                    picker="year"
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  colon={false}
                  messageVariables={{
                    label: `©${t('submitMusic.name')}`,
                  }}
                  rules={rules.copyrightName}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      ©{t('submitMusic.name')}:
                    </div>
                  }
                  name="copyrightName"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.name')}
                  />
                </Form.Item>
                <Form.Item
                  className="col-start-1 s-submit-input-label"
                  labelAlign="left"
                  colon={false}
                  messageVariables={{
                    label: `℗${t('submitMusic.year')}`,
                  }}
                  rules={rules.phonogramCopyrightYear}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      ℗{t('submitMusic.year')}:
                    </div>
                  }
                  name="phonogramCopyrightYear"
                >
                  <DatePicker
                    className="s-submit-datepicker !h-42px"
                    placeholder={t('submitMusic.placeholders.datePicker')}
                    picker="year"
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  colon={false}
                  messageVariables={{
                    label: `℗${t('submitMusic.name')}`,
                  }}
                  rules={rules.phonogramCopyright}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      ℗{t('submitMusic.name')}:
                    </div>
                  }
                  name="phonogramCopyright"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.name')}
                  />
                </Form.Item>
              </div>
            </div>

            <div className="mb-10 flex ">
              {/* 音频格式Section */}
              <div>
                <h2 className="text-white text-[22px] font-bold mb-8 ">
                  {t('submitMusic.audioFormatOptional')}
                </h2>

                <div className="w-[454px]">
                  <Form.Item
                    className="s-submit-input-label"
                    labelAlign="left"
                    colon={false}
                  >
                    <Checkbox.Group className="s-submit-checkbox">
                      <div className="flex flex-col gap-24px">
                        {audioFormats.map(audioFormat => (
                          <Checkbox value={audioFormat.code}>
                            {audioFormat.name}
                          </Checkbox>
                        ))}
                      </div>
                    </Checkbox.Group>
                  </Form.Item>
                </div>
              </div>
              <div>
                <h2 className="text-white text-[22px] font-bold mb-8 ">
                  {t('submitMusic.releaseOptions')}
                </h2>
                <div className="w-[454px]">
                  <Form.Item
                    className="s-submit-input-label"
                    labelAlign="left"
                    colon={false}
                  >
                    <Checkbox.Group className="s-submit-checkbox">
                      <div className="flex flex-col gap-24px">
                        {releaseOptions.map(releaseOption => (
                          <Checkbox value={releaseOption.code}>
                            {releaseOption.name}
                          </Checkbox>
                        ))}
                      </div>
                    </Checkbox.Group>
                  </Form.Item>
                </div>
              </div>
            </div>

            <div className="mb-16 flex items-center gap-40px">
              <h2 className="text-white text-[22px] font-bold mb-8 ">
                {t('submitMusic.additionalMediaFiles')}
              </h2>

              <MusicUpload onUploadSuccess={handleAdditionalUploadSuccess} />
            </div>

            <div className="flex justify-center gap-6 mb-60px">
              <Button
                size="large"
                className="w-[496px] h-[63px] bg-transparent rounded-6px text-primary text-[16px] font-bold border border-primary hover:opacity-45 hover:!bg-transparent  transition-colors"
                onClick={handleCancel}
              >
                {t('submitMusic.form.cancel')}
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={loading}
                className="w-[496px] h-[63px] bg-primary text-black text-[16px] font-bold border-none rounded-6px hover:opacity-45 transition-colors"
              >
                {t('submitMusic.form.submit')}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default SubmitMusic;
